# 数据迁移文档：V3_23_1_101__JavaMigration

## 概述
此迁移脚本用于将 `OrderPushDownRecordEntity`（单据下推记录表）中的数据迁移到 `OrderPushDownIdentifierEntity`（单据下推标识表）。

## 迁移范围
**仅处理以下单据类型的记录：**
- `workOrderMaterialList` - 生产工单用料清单

**重要说明：**
- `sourceOrderMaterialId` 对应 `WorkOrderMaterialListMaterialEntity` 的 `id` 字段（物料行ID）
- 不是主表 `WorkOrderMaterialListEntity` 的 `materialListId`

## 字段映射关系

| 源表字段 (OrderPushDownRecordEntity) | 目标表字段 (OrderPushDownIdentifierEntity) | 说明 |
|-------------------------------------|-------------------------------------------|------|
| sourceOrderType | orderType | 源单据类型 |
| sourceOrderMaterialId | orderMaterialId | 单据物料行ID（转为字符串） |
| sourceOrderBatch | batchNumber | 批次号 |
| targetOrderType | targetOrderType | 目标单据类型 |
| 计算得出 | state | 下推标识状态 |

## 状态计算逻辑

下推标识状态根据同一组记录的下推情况计算：

1. **未下推 (noPushDown)**: 没有成功的下推记录
2. **已下推 (allPushDown)**: 所有记录都成功下推
3. **部分下推 (partPushDown)**: 部分记录成功下推

### 成功下推的判断条件：
- 有目标单据编号 (`targetOrderNumber` 不为空)
- 且没有异常标记 (`isAbnormal` 为 null 或 false)

## 分组逻辑
记录按以下键值分组去重：
```
{sourceOrderType}_{sourceOrderMaterialId}_{batchNumber}
```

## 性能优化
- **数据库层面过滤**：直接在SQL查询中过滤单据类型，避免查询全表
- **条件查询**：使用 `IN` 和 `IS NOT NULL` 条件，减少数据传输
- **分批处理**：每1000条记录批量插入一次
- **错误隔离**：单个记录处理失败不影响其他记录
- **重复检查**：跳过已存在的标识记录

## 执行流程
1. 直接查询生产工单相关下推记录（数据库层面过滤）
2. 按分组键去重
3. 检查已存在记录
4. 批量插入新记录
5. 输出统计信息

## 查询优化
```sql
SELECT * FROM dfs_order_push_down_record
WHERE source_order_type = 'workOrderMaterialList'
  AND source_order_material_id IS NOT NULL
```

**数据关系说明：**
- `source_order_material_id` 存储的是 `dfs_work_order_material_list_material.id`
- 这是物料行的唯一标识，不是主表的ID

## 日志输出
- 生产工单相关记录数统计
- 分组后唯一组合数
- 处理进度和结果统计

## 安全性
- 事务回滚：迁移失败时自动回滚
- 幂等性：可重复执行，不会产生重复数据
- 错误处理：详细的错误日志和异常处理
